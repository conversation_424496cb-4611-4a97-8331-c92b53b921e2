<div class="ajax-text-and-image white-popup-block">
	<style>
	.ajax-text-and-image {
		max-width:800px; margin: 20px auto; background: #FFF; padding: 0; line-height: 0;
	}
	.ajcol {
		width: 50%; float:left;
	}
	.ajcol img {
		width: 100%; height: auto;
	}
	@media all and (max-width:30em) {
		.ajcol { 
			width: 100%;
			float:none;
		}
	}
	</style>
	<div class="ajcol">
		<img src="http://upload.wikimedia.org/wikipedia/commons/thumb/e/e7/Marabou_stork%2C_Leptoptilos_crumeniferus_edit1.jpg/603px-Marabou_stork%2C_Leptoptilos_crumeniferus_edit1.jpg"/>
	</div>
	<div class="ajcol" style="line-height: 1.231;">
		<div style="padding: 1em">
			<h1>This is just block of HTML, loaded via ajax</h1>
		    <p>You can put absolutely any HTML here and resize it dynamically just with help of CSS.</p>
		</div>
	</div>
	<div style="clear:both; line-height: 0;"></div>
</div>