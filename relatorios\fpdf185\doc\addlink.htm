<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>AddLink</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>AddLink</h1>
<code><b>int</b> AddLink()</code>
<h2>Description</h2>
Creates a new internal link and returns its identifier. An internal link is a clickable area
which directs to another place within the document.
<br>
The identifier can then be passed to Cell(), Write(), Image() or Link(). The destination is
defined with SetLink().
<h2>See also</h2>
<a href="cell.htm">Cell</a>,
<a href="write.htm">Write</a>,
<a href="image.htm">Image</a>,
<a href="link.htm">Link</a>,
<a href="setlink.htm">SetLink</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
