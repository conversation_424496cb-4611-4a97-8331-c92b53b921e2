/* Nova Tabela de Produtos - Design Moderno */

/* Reset de fonte global para toda a tabela */
.produtos-table-header,


/* Container Principal da <PERSON><PERSON> */
.produtos-table-header {
    height: 48px;
    overflow-y: auto;
    position: relative;
    background: white;
    color: #212121;
    padding: 0 10px;
    border-radius: 1px 1px 0 0;
    border-bottom: 1px solid #c7c6c6;
    box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
}




.produtos-header-row {
    font-weight: 600;
    font-size: 13px;
    letter-spacing: 0.5px;
    display: grid;
    grid-template-columns: 95px 125px 1fr 100px 80px;
    gap: 1px;
    padding: 0;
    margin: 0;
}

.produtos-col {
    font-weight: 600;
    font-size: 13px;
    padding: 15px 12px;
    border: 1px solid #e0e0e0;
    text-align: center;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
}


.produtos-col:last-child {
    border-right: none;
}

.produtos-col-descricao {
    text-align: center;
    justify-content: center;
}

/* Container da Tabela com Scroll */
.produtos-table-container {
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-top: none;
    border-radius: 0 0 8px 8px;
    background: white;
}

.produtos-table-body {
    min-height: 200px;
}

/* Linhas da Tabela */
.produtos-row {
    display: grid;
    grid-template-columns: 100px 130px 1fr 100px 80px;
    gap: 1px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
    background: white;
}

.produtos-row:hover {
    background: #f8f9fa;
}

.produtos-row:last-child {
    border-bottom: none;
}

/* Células da Tabela */
.produtos-cell {
    padding: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 50px;
    border-right: 1px solid #f0f0f0;
}

.produtos-cell:last-child {
    border-right: none;
}

.produtos-cell-descricao {
    justify-content: center;
    text-align: center;
    font-size: 14px;
    line-height: 1.4;
}

.produtos-cell-codigo {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #222222;
}

.produtos-cell-origem {
    justify-content: center;
}

.produtos-cell-editar {
    justify-content: center;
}

.produtos-cell-checkbox {
    justify-content: center;
}

/* Ícones de Origem */
.origem-icon {
    font-size: 20px !important;
    transition: transform 0.2s ease;
}

.origem-icon:hover {
    transform: scale(1.1);
}

.origem-icon.local {
    color: #424242;
}

.origem-icon.nuvemshop-normal {
    color: #2196F3;
}

.origem-icon.nuvemshop-vitrine {
    color: #2E7D32;
}

.origem-icon.nuvemshop-variante {
    color: #4CAF50;
}

.origem-icon.nuvemshop-legacy {
    color: #757575;
}

/* Checkbox Customizado - Remover checkbox padrão do Materialize */
.produtos-checkbox {
    position: relative;
    margin: 0;
}

.produtos-checkbox input[type="checkbox"] {
    opacity: 0;
    position: absolute;
    width: 20px;
    height: 20px;
    margin: 0;
    z-index: 2;
}

.produtos-checkbox label {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 3px;
    background: white;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    z-index: 1;
}

.produtos-checkbox input[type="checkbox"]:checked + label {
    background: #2196F3;
    border-color: #2196F3;
}

.produtos-checkbox input[type="checkbox"]:checked + label:after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 3px;
    color: white;
    font-size: 14px;
    font-weight: bold;
}

/* Esconder checkbox padrão do Materialize */
.produtos-checkbox input[type="checkbox"] + label:before,
.produtos-checkbox input[type="checkbox"] + label:after {
    display: none !important;
}

/* Forçar estilo customizado */
.produtos-cell-checkbox [type="checkbox"] + label:before,
.produtos-cell-checkbox [type="checkbox"] + label:after {
    display: none !important;
}

/* Botão Editar */
.produtos-btn-edit {
    background-color: #2196F3 !important;
    color: white;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.produtos-btn-edit:hover {
    background: #616161;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.produtos-btn-edit i {
    font-size: 18px;
}

/* Estado de Loading */
.produtos-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #666;
    font-size: 16px;
}

.produtos-loading i {
    margin-right: 10px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Estado Vazio */
.produtos-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #999;
    text-align: center;
}

.produtos-empty i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.produtos-empty h6 {
    margin: 0 0 8px 0;
    color: #666;
}

.produtos-empty p {
    margin: 0;
    font-size: 14px;
}

/* Responsividade - Tablets */
@media (max-width: 768px) {
    .produtos-header-row,
    .produtos-row {
        grid-template-columns: 60px 100px 1fr 80px 60px;
        gap: 0;
    }

    .produtos-col,
    .produtos-cell {
        padding: 8px 6px;
        font-size: 12px;
    }

    .produtos-col-descricao,
    .produtos-cell-descricao {
        font-size: 13px;
    }

    .origem-icon {
        font-size: 18px !important;
    }

    .produtos-btn-edit {
        width: 32px;
        height: 32px;
    }

    .produtos-btn-edit i {
        font-size: 16px;
    }

    #paginacao_superior{
        display: none;
    }
}

/* Responsividade Mobile - Scroll Horizontal */
@media (max-width: 600px) {
    /* Esconder cabeçalho original */
    .produtos-table-header {
        display: none;
    }

    /* Container com scroll horizontal */
    .produtos-table-container {
        overflow-x: auto !important;
        overflow-y: hidden !important;
        -webkit-overflow-scrolling: touch;
        padding-bottom: 15px;
        max-height: none;
    }

    /* Body da tabela como flex horizontal */
    .produtos-table-body {
        display: flex !important;
        flex-wrap: nowrap !important;
        gap: 16px !important;
        padding: 10px 0 !important;
        width: max-content !important;
        min-width: 100% !important;
        min-height: auto !important;
    }

    /* Cada linha vira um card */
    .produtos-row {
        display: block !important;
        width: 280px !important;
        min-width: 280px !important;
        flex-shrink: 0 !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 8px !important;
        background: white !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        padding: 16px !important;
        margin: 0 !important;
    }

    /* Células viram linhas com labels */
    .produtos-cell {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 8px 0 !important;
        border-right: none !important;
        border-bottom: 1px solid #f0f0f0 !important;
        min-height: auto !important;
    }

    .produtos-cell:last-child {
        border-bottom: none !important;
    }

    /* Labels para cada campo */
    .produtos-cell:before {
        content: attr(data-label);
        font-weight: 600;
        color: #020202;
        font-size: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        flex-shrink: 0;
        width: 80px;
    }

    .produtos-cell-checkbox:before {
        content: "E-Commerce";
    }

    .produtos-cell-codigo:before {
        content: "Código";
    }

    .produtos-cell-descricao:before {
        content: "Produto";
    }

    .produtos-cell-origem:before {
        content: "Plataformas";
    }

    .produtos-cell-editar:before {
        content: "Ações";
    }

    /* Ajustar conteúdo das células */
    .produtos-cell-descricao {
        flex-direction: column !important;
        align-items: flex-start !important;
        text-align: left !important;
    }

    .produtos-cell-codigo {
        font-family: 'Courier New', monospace;
        font-size: 13px;
    }

    /* Indicador de scroll */
    .produtos-table-container:after {
        content: "⟺ Deslize para ver mais produtos";
        display: block;
        text-align: center;
        padding: 8px;
        color: #757575;
        font-size: 12px;
        background-color: rgba(255,255,255,0.9);
        border-top: 1px solid #e0e0e0;
        position: sticky;
        bottom: 0;
        left: 0;
        right: 0;
    }
}

/* Mobile muito pequeno */
@media (max-width: 480px) {
    .produtos-row {
        width: 260px !important;
        min-width: 260px !important;
        padding: 12px !important;
    }

    .produtos-cell:before {
        width: 70px;
        font-size: 11px;
    }

    .produtos-btn-edit {
        width: 28px;
        height: 28px;
    }

    .produtos-btn-edit i {
        font-size: 14px;
    }
}

/* Scrollbar Customizada */
.produtos-table-container::-webkit-scrollbar {
    width: 8px;
}

.produtos-table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.produtos-table-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.produtos-table-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Animações */
.produtos-row {
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 370px) {
    .pagination li a {
        font-size: 1.2rem;
    }
    .pagination .material-icons{
        display: none;
    }
}

/* Print Styles */
@media print {
    .produtos-table-container {
        max-height: none;
        overflow: visible;
    }

    .produtos-btn-edit {
        display: none;
    }

    .produtos-checkbox {
        display: none;
    }
}
